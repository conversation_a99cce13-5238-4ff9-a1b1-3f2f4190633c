"""
Carousel prompt generation utilities for LinkedIn carousel creation.

DYNAMIC STYLE INJECTION SYSTEM
===============================

Problem: Previous approach told AI to "use same colors as Slide 1" but didn't provide them.
Solution: Slide 1 generates colors → We extract them → Slides 2+ inject exact hex codes.

Usage Example:
--------------
```python
# Step 1: Generate Slide 1 with NO style_memory
prompt_1 = get_carousel_prompt(
    content=content,
    slide_number=1,
    total_slides=5,
    slide_content=slides[0],
    main_topic="How to Scale Your Business",
    style_memory=None  # Slide 1 generates colors
)

# Step 2: Send prompt_1 to AI and get response
ai_response_1 = call_ai_image_api(prompt_1)

# Step 3: Extract colors from AI's response
style_memory = extract_style_memory(ai_response_1)
# Returns: {
#   'primary': '#2D5F3F',
#   'secondary': '#1A1A1A', 
#   'accent': '#FFD700',
#   'background': 'solid #2D5F3F'
# }

# Step 4: Generate Slides 2-5 WITH style_memory
for i in range(2, 6):
    prompt_i = get_carousel_prompt(
        content=content,
        slide_number=i,
        total_slides=5,
        slide_content=slides[i-1],
        main_topic="How to Scale Your Business",
        style_memory=style_memory  # Inject exact colors
    )
    ai_response_i = call_ai_image_api(prompt_i)
```

Benefits:
---------
✓ Perfect color consistency (exact hex codes, not "similar" colors)
✓ No AI hallucination of new colors mid-carousel
✓ Deterministic, reproducible color schemes
✓ Reduced token waste (no need to explain "use same colors")
"""
import re
from typing import List, Dict, Optional, Tuple


def extract_style_memory(ai_response: str) -> Optional[Dict[str, str]]:
    """
    Extract color palette from Slide 1 AI response.
    
    The AI is instructed to output colors in this format:
    **CAROUSEL_COLORS:**
    - PRIMARY: #XXXXXX (description)
    - SECONDARY: #XXXXXX (description)
    - ACCENT: #XXXXXX (description)
    - BACKGROUND: solid #XXXXXX or gradient #XXXXXX to #XXXXXX
    
    Args:
        ai_response: The AI's text response from Slide 1 generation
    
    Returns:
        Dict with keys 'primary', 'secondary', 'accent', 'background' or None if not found
    """
    style_memory = {}
    
    # Look for the CAROUSEL_COLORS section
    color_section = re.search(r'\*\*CAROUSEL_COLORS:\*\*(.*?)(?=\n\n|\Z)', ai_response, re.DOTALL | re.IGNORECASE)
    
    if not color_section:
        return None
    
    color_text = color_section.group(1)
    
    # Extract PRIMARY color
    primary_match = re.search(r'PRIMARY:\s*(#[0-9A-Fa-f]{6})', color_text, re.IGNORECASE)
    if primary_match:
        style_memory['primary'] = primary_match.group(1).upper()
    
    # Extract SECONDARY color
    secondary_match = re.search(r'SECONDARY:\s*(#[0-9A-Fa-f]{6})', color_text, re.IGNORECASE)
    if secondary_match:
        style_memory['secondary'] = secondary_match.group(1).upper()
    
    # Extract ACCENT color
    accent_match = re.search(r'ACCENT:\s*(#[0-9A-Fa-f]{6})', color_text, re.IGNORECASE)
    if accent_match:
        style_memory['accent'] = accent_match.group(1).upper()
    
    # Extract BACKGROUND (can be "solid #XXXXXX" or "gradient #XXXXXX to #XXXXXX")
    background_match = re.search(r'BACKGROUND:\s*(.+?)(?=\n|$)', color_text, re.IGNORECASE)
    if background_match:
        style_memory['background'] = background_match.group(1).strip()
    
    # Validate we got all required colors
    if len(style_memory) >= 3:  # At minimum primary, secondary, accent
        return style_memory
    
    return None


def get_golden_examples() -> str:
    """
    Concise examples of high-quality carousel outputs for few-shot learning.
    """
    return """
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✨ QUALITY EXAMPLES
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**Ex 1 - Finance**: Green #2D5F3F + Gold #FFD700 | Intro: "Scaled SaaS to $1M ARR" + arrow | Content: "01" badge, specific headline, 3 bullets (8-15 words each) | Same colors all slides ✓

**Ex 2 - Tech**: Purple #6B46C1 + Cyan #00E5FF | Intro: "5 AI Tools Save 10 Hours Weekly" + icon | Content: numbered badge, benefit headline, bullets | Gradient background consistent ✓

**Ex 3 - Marketing**: Orange #FF6B35 + Yellow #FFEB3B | CTA: "Follow for tips" + bookmark icon | Simple visuals, clear hierarchy ✓

**Key Patterns**: Same colors throughout | Specific headlines | Scannable bullets | 1-2 visuals per slide | Mobile-readable
"""


def get_dynamic_color_guidelines() -> str:
    """
    Minimal color generation guidelines.
    """
    return """
🎨 **COLOR RULES**: Analyze content → Generate Primary/Secondary/Accent (#hex) + Background (solid/gradient). Match colors to theme (green=growth, purple=innovation, orange=energy). Maintain 4.5:1+ contrast. Be bold.
"""


def extract_carousel_slides(content: str, max_slides: int = 8) -> List[str]:
    """
    Intelligently extract and structure content into carousel slides.
    Ensures no content repetition and maintains thematic consistency.
    Dynamically adapts to content length and structure.

    MINIMUM total slides: 4 (1 intro + 2 content + 1 CTA)
    MAXIMUM total slides: 10 (1 intro + up to 8 content + 1 CTA)

    Args:
        content: Raw content to be split into slides
        max_slides: Maximum number of content slides (default: 8, excluding intro and CTA)

    Returns:
        List of unique slide content strings (minimum 2, maximum 8 content slides)
    """
    # Split content into lines and filter empty ones
    lines = [line.strip() for line in content.split('\n') if line.strip()]
    
    slides = []
    
    # Check if content has bullet points or numbered lists
    bullet_lines = [l for l in lines if re.match(r'^[\-\*\+•]\s+', l) or re.match(r'^\d+[\.\)]\s+', l)]
    
    if bullet_lines:
        # If we have structured content, each bullet becomes a slide
        # Dynamically limit based on content amount (minimum 2, maximum 8)
        num_bullets = len(bullet_lines)
        # Default to 2 content slides, but allow more if there are many bullets
        if num_bullets <= 2:
            slides_to_extract = 2  # Minimum 2 content slides
        elif num_bullets <= 4:
            slides_to_extract = min(num_bullets, max_slides)  # Use actual number if 3-4 bullets
        else:
            # For 5+ bullets, cap at max_slides
            slides_to_extract = min(num_bullets, max_slides)

        # Track used content to prevent repetition
        used_content = set()

        for line in bullet_lines[:slides_to_extract]:
            # Remove bullet/number markers
            cleaned = re.sub(r'^[\-\*\+•\d\.\)]+\s+', '', line).strip()

            # Check for content uniqueness (avoid near-duplicates)
            content_key = ' '.join(sorted(cleaned.lower().split()[:5]))  # Use first 5 words as key
            if content_key not in used_content and len(cleaned) > 10:
                slides.append(cleaned)
                used_content.add(content_key)
    else:
        # Split by sentences or paragraphs
        # Try to split into meaningful chunks
        text_blob = ' '.join(lines)
        sentences = re.split(r'[.!?]+', text_blob)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 15]
        
        # Group sentences into slides (3-5 sentences per slide or 60-80 words for content slides)
        # Dynamically determine target number of slides based on content length
        total_words = len(text_blob.split())
        
        if total_words < 200:
            target_slides = 2  # Short content: minimum 2 slides
            words_per_slide = 50  # Shorter slides for brief content
        elif total_words < 400:
            target_slides = 3  # Medium content: 3 slides (5 total)
            words_per_slide = 70  # Medium slides
        elif total_words < 600:
            target_slides = 4  # Medium-long content: 4 slides (6 total)
            words_per_slide = 80  # Longer slides
        else:
            target_slides = min(8, max_slides)  # Long content: up to 8 slides
            words_per_slide = 90  # Longer slides for detailed content
        
        current_slide = []
        current_word_count = 0
        used_sentences = set()  # Track used sentences to prevent repetition

        for sentence in sentences:
            words = sentence.split()
            word_count = len(words)

            # Create a key to check for similar content
            sentence_key = ' '.join(sorted(sentence.lower().split()[:3]))  # Use first 3 words as key

            # Skip if we've already used similar content
            if sentence_key in used_sentences:
                continue

            # Allow more words per slide for better content depth
            if current_word_count + word_count <= words_per_slide and len(current_slide) < 5:
                current_slide.append(sentence)
                current_word_count += word_count
                used_sentences.add(sentence_key)
            else:
                # Save current slide and start new one
                if current_slide:
                    slide_content = '. '.join(current_slide) + '.'
                    slides.append(slide_content)
                current_slide = [sentence]
                current_word_count = word_count
                used_sentences.add(sentence_key)

            # Stop if we have enough slides
            if len(slides) >= target_slides:
                break
        
        # Add remaining content if we haven't reached minimum slides
        if current_slide and len(slides) < target_slides:
            slides.append('. '.join(current_slide) + '.')
        
        # Ensure we have at least 2 content slides
        if len(slides) < 2 and sentences:
            # Find truly unique sentences not already used
            used_content_keys = set()
            for slide in slides:
                slide_key = ' '.join(sorted(slide.lower().split()[:3]))
                used_content_keys.add(slide_key)

            remaining_sentences = []
            for sent in sentences:
                sent_key = ' '.join(sorted(sent.lower().split()[:3]))
                if sent_key not in used_content_keys and len(sent.strip()) > 10:
                    remaining_sentences.append(sent)
                    used_content_keys.add(sent_key)

            # Add unique remaining sentences
            for sent in remaining_sentences[:2 - len(slides)]:
                slides.append(sent + '.')
    
    # Ensure we return between 2 and max_slides (MINIMUM 2 for 4 total slides with intro + CTA)
    final_slides = slides[:max_slides]

    # CRITICAL: Enforce minimum of 2 content slides (results in 4 total slides)
    if len(final_slides) < 2:
        if len(final_slides) == 0:
            # No slides extracted - create 2 generic slides from content
            text_blob = ' '.join(lines) if lines else content
            words = text_blob.split()
            # Split into 2 roughly equal parts
            chunk_size = max(20, len(words) // 2)
            for i in range(2):
                start_idx = i * chunk_size
                end_idx = start_idx + chunk_size if i < 1 else len(words)
                slide_text = ' '.join(words[start_idx:end_idx])
                if slide_text:
                    final_slides.append(slide_text)
        elif len(final_slides) == 1:
            # Only 1 slide - split it into 2 distinct parts
            slide_words = final_slides[0].split()
            if len(slide_words) >= 20:
                mid_point = len(slide_words) // 2
                final_slides = [
                    ' '.join(slide_words[:mid_point]),
                    ' '.join(slide_words[mid_point:])
                ]
            else:
                # Too short - create complementary content instead of duplication
                original_content = final_slides[0]
                # Extract key concepts for the second slide
                key_words = [word for word in slide_words if len(word) > 4][:3]
                if key_words:
                    second_slide = f"Key aspects: {', '.join(key_words)}. Implementation and practical considerations."
                else:
                    second_slide = "Implementation strategies and best practices for optimal results."

                final_slides = [original_content, second_slide]

    
    # Ensure exactly minimum 2 slides are returned
    if len(final_slides) < 2:
        # Last resort: create complementary content instead of duplication
        if len(final_slides) == 1:
            # Create a second slide that complements the first
            original = final_slides[0]
            words = original.split()
            if len(words) > 10:
                # Extract key concepts for a complementary slide
                key_concepts = [word for word in words if len(word) > 4][:3]
                if key_concepts:
                    complementary = f"Key implementation strategies: {', '.join(key_concepts)}. Practical application and best practices."
                else:
                    complementary = "Implementation strategies and practical considerations for optimal results."
                final_slides.append(complementary)
            else:
                final_slides.append("Practical implementation and actionable next steps.")
        elif len(final_slides) == 0:
            # Create two distinct slides from content
            final_slides = [
                "Core concepts and fundamental principles.",
                "Practical implementation and actionable strategies."
            ]
    
    return final_slides


def get_carousel_prompt(
    content: str, 
    slide_number: int, 
    total_slides: int, 
    slide_content: str, 
    main_topic: str,
    style_memory: Dict[str, str] = None
) -> str:
    """
    Generate a prompt for creating a single carousel slide using GPT Image 1.5.
    
    Dynamic Style Injection:
    - Slide 1: Generates colors and returns them in the prompt for extraction
    - Slides 2+: Injects exact hex codes from style_memory to ensure consistency
    
    Args:
        content: Full original content for context
        slide_number: Current slide number (1-based, where 1 is intro)
        total_slides: Total number of slides in carousel
        slide_content: Content for this specific slide
        main_topic: Main topic/title extracted from content
        style_memory: Dict with keys 'primary', 'secondary', 'accent', 'background' (hex codes)
                     None for Slide 1 (generates colors), Required for Slides 2+
    
    Returns:
        Detailed prompt for GPT Image 1.5 with dynamic color generation or injection
    """
    
    # Determine slide type
    is_intro = (slide_number == 1)
    is_conclusion = (slide_number == total_slides)
    is_content = not is_intro and not is_conclusion
    
    # Create slide position strings to avoid f-string nesting issues
    slide_position = f"{slide_number}/{total_slides}"
    content_slides_range = f"2-{total_slides-1}"

    # Generate color instructions based on slide number
    if slide_number == 1:
        # Slide 1: Include golden examples + generate colors
        golden_examples = get_golden_examples()
        color_instructions = get_dynamic_color_guidelines() + """

⚠️ **COLOR OUTPUT**: Document your palette in this format:
**CAROUSEL_COLORS:**
- PRIMARY: #XXXXXX (description)
- SECONDARY: #XXXXXX (description)
- ACCENT: #XXXXXX (description)
- BACKGROUND: [solid #XXXXXX | gradient #XXXXXX to #XXXXXX]
"""
    else:
        # Slides 2+: NO examples, just inject colors
        golden_examples = ""  # Skip examples for slides 2+
        
        if not style_memory:
            raise ValueError(f"style_memory is required for slide {slide_number}")
        
        primary = style_memory.get('primary', '#000000')
        secondary = style_memory.get('secondary', '#FFFFFF')
        accent = style_memory.get('accent', '#FF0000')
        background = style_memory.get('background', f'solid {primary}')
        
        color_instructions = f"""
🎨 **INJECTED COLORS** (Use these EXACT colors):
PRIMARY: {primary} | SECONDARY: {secondary} | ACCENT: {accent} | BACKGROUND: {background}
⛔ DO NOT generate new colors - use these exact hex codes!
"""
    
    # Base specifications for LinkedIn carousels
    base_specs = f"""
{golden_examples if golden_examples else ''}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📱 CURRENT TASK
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**Carousel**: Slide {slide_position} of {total_slides} | Dimensions: 1080x1080px (1:1 ratio)
**Content Preview**: {content[:300]}{"..." if len(content) > 300 else ""}

{color_instructions}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📐 SPECS
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**Size**: 1080x1080px | 60-80px margins | Mobile-optimized
**Type**: Headlines 48-72pt bold | Body 24-32pt | Words: Intro(20-30), Content(40-80), CTA(15-25)
**Visuals**: Pick 2-3: icons, shapes, charts, arrows, badges, large numbers
**Format**: Bullets not paragraphs | 8-15 words/line | 3-4 points max
"""

    if is_intro:
        # First slide: Hook and title
        prompt = f'''{base_specs}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 INTRO SLIDE (Slide {slide_position})
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**Structure**:
• Top: "SWIPE TO LEARN →" (12-16pt, subtle)
• Center: HERO HEADLINE (48-72pt, bold, 20-30 words max, benefit-driven)
• Bottom: 1 primary visual + 1-2 supporting elements
• Corner: "{slide_position}" indicator

**Topic**: {main_topic}

**Task**: Create attention-grabbing intro. Generate color palette AND document it in CAROUSEL_COLORS format.
'''

    elif is_conclusion:
        # Last slide: CTA and closing
        prompt = f'''{base_specs}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 CTA SLIDE (Slide {slide_position})
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**Structure**:
• Top: "Ready to Start?" or "Key Takeaway" (32-40pt)
• Center: CTA statement (36-48pt, 15-25 words max, engagement-focused)
• Bottom: Action icon + 1-2 decorative elements
• Corner: "{slide_position}" indicator

**CTA Examples**: "Follow for more" | "Save this" | "Which tip will you try first?"

**Task**: Drive engagement. Use INJECTED colors above (do not generate new colors). Keep conversational.
'''

    else:
        # Content slides: Main points
        prompt = f'''{base_specs}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 CONTENT SLIDE (Slide {slide_position})
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**Structure**:
• Top: Number badge "{str(slide_number-1).zfill(2)}" (40-56pt, in colored circle/square)
• Middle: UNIQUE headline (32-44pt, specific to THIS slide, not generic)
  + 3-4 bullets/numbers (8-15 words each, 40-80 words total)
• Bottom: 1 primary visual + 1-2 supporting elements
• Corner: "{slide_position}" indicator

**Content**: {slide_content}

**Task**: Create SPECIFIC headline for this point (not carousel title). Use bullets not paragraphs. Use INJECTED colors above.

⛔ **CRITICAL**: 
• Each slide needs DIFFERENT headline - avoid repetition!
• Use INJECTED colors only - do NOT generate new colors!
'''

    # Add minimal footer
    action_footer = f"""

✅ **Must Have**: 1080x1080px | 24pt+ text | {'Generate colors' if slide_number == 1 else 'Use injected colors'} | Bullets not paragraphs | Slide {slide_position}
"""

    return prompt + action_footer
