from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.responses import StreamingResponse, JSONResponse, FileResponse
from fastapi.exceptions import RequestValidationError
from app.schemas import PromptRequest, ComplianceResponse, ImageGenerationResponse
from app.imagen import generate_image
from app.config import COMPLIANCE_MODE
from app.prompts import get_infographic_prompt, preprocess_content
from io import BytesIO
from PIL import Image
import io
import re
from typing import Dict, List, Tuple
import random
import logging
import uvicorn
import os
import tempfile
import uuid
import requests
from openai import OpenAI
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Initialize compliance filter based on configuration
if COMPLIANCE_MODE == "ai":
    from app.ai_compliance import ai_compliance_filter as compliance_filter
    logger.info("Using AI-based compliance filter (context-aware)")
else:
    from app.compliance import compliance_filter
    logger.info("Using keyword-based compliance filter")

# Initialize OpenAI client for infographic generation
openai_api_key = os.getenv("OPENAI_API_KEY")
openai_client = None

if openai_api_key and openai_api_key != "YOUR_API_KEY_HERE":
    try:
        openai_client = OpenAI(api_key=openai_api_key)
        logger.info("OpenAI client initialized successfully for infographic generation.")
    except Exception as e:
        logger.error(f"Failed to initialize OpenAI client: {e}", exc_info=True)
        openai_client = None
else:
    logger.warning("OpenAI API key not found. Infographic generation will be unavailable.")

app = FastAPI(
    title="LinkedIn Image & Infographic Generator",
    description="API for generating professional images and infographics for LinkedIn posts with compliance filtering",
    version="2.0.0"
)

# Professional color palettes for LinkedIn
PROFESSIONAL_COLORS = {
    'corporate': ['#0077B5', '#000000', '#FFFFFF', '#F5F5F5'],  # LinkedIn blue and professional neutrals
    'modern': ['#2D3E50', '#3498DB', '#ECF0F1', '#FFFFFF'],     # Modern professional
    'tech': ['#1A73E8', '#34A853', '#FBBC05', '#EA4335'],       # Tech-inspired
    'creative': ['#6B5B95', '#878787', '#F7CAC9', '#92A8D1'],   # Creative professional
    'minimal': ['#2C3E50', '#E74C3C', '#ECF0F1', '#FFFFFF']     # Minimal professional
}

# Visual style elements
VISUAL_STYLES = {
    'composition': [
        'centered composition with clear focal point',
        'rule of thirds composition',
        'balanced visual hierarchy',
        'clean and minimal layout',
        'dynamic composition with leading lines'
    ],
    'lighting': [
        'natural professional lighting',
        'soft diffused lighting',
        'dramatic professional lighting',
        'even balanced lighting',
        'subtle directional lighting'
    ],
    'mood': [
        'professional and confident',
        'innovative and forward-thinking',
        'collaborative and engaging',
        'successful and accomplished',
        'inspiring and motivational'
    ]
}

def analyze_post_content(text: str) -> Dict[str, List[str]]:
    """
    Analyze the post content to determine appropriate visual elements.
    """
    text = text.lower()
    analysis = {
        'mood': [],
        'style': [],
        'colors': []
    }
    
    # Analyze mood and tone
    if any(word in text for word in ['excited', 'thrilled', 'amazing', 'incredible']):
        analysis['mood'].append('energetic and positive')
    if any(word in text for word in ['success', 'achievement', 'milestone']):
        analysis['mood'].append('accomplished and proud')
    if any(word in text for word in ['learn', 'growth', 'development']):
        analysis['mood'].append('educational and insightful')
    if any(word in text for word in ['team', 'collaboration', 'together']):
        analysis['mood'].append('collaborative and united')
    
    # Determine style
    if any(word in text for word in ['tech', 'digital', 'innovation']):
        analysis['style'].append('modern and technological')
    if any(word in text for word in ['creative', 'design', 'art']):
        analysis['style'].append('creative and artistic')
    if any(word in text for word in ['business', 'corporate', 'professional']):
        analysis['style'].append('corporate and professional')
    
    # Select color palette
    if 'tech' in text or 'digital' in text:
        analysis['colors'] = PROFESSIONAL_COLORS['tech']
    elif 'creative' in text or 'design' in text:
        analysis['colors'] = PROFESSIONAL_COLORS['creative']
    else:
        analysis['colors'] = PROFESSIONAL_COLORS['corporate']
    
    return analysis

def generate_visual_prompt(content_analysis: Dict[str, List[str]], key_elements: List[str]) -> str:
    """
    Generate a detailed visual prompt based on content analysis.
    """
    # Select random elements from each category for variety
    composition = random.choice(VISUAL_STYLES['composition'])
    lighting = random.choice(VISUAL_STYLES['lighting'])
    mood = random.choice(VISUAL_STYLES['mood'])
    
    # Get primary colors
    primary_color = content_analysis['colors'][0]
    secondary_color = content_analysis['colors'][1]
    
    prompt_parts = [
        f"Create a professional LinkedIn post image that represents: {' '.join(key_elements)}",
        "\nVisual Style:",
        f"- {composition}",
        f"- {lighting}",
        f"- Mood: {mood}",
        f"- Primary color: {primary_color}",
        f"- Secondary color: {secondary_color}",
        "\nTechnical Requirements:",
        "- High-resolution photorealistic image",
        "- Professional and modern aesthetic",
        "- Clean and uncluttered composition",
        "- No text overlays or watermarks",
        "- Suitable for LinkedIn professional audience",
        "- Aspect ratio: 1:1 (square)",
        "\nAdditional Guidelines:",
        "- Focus on visual storytelling",
        "- Ensure professional representation",
        "- Maintain brand consistency",
        "- Create engaging visual hierarchy",
        "- Use subtle depth and dimension"
    ]
    
    return '\n'.join(prompt_parts)

def clean_html_content(html_text: str) -> str:
    """
    Clean HTML content and extract meaningful text.
    """
    # Remove HTML tags
    clean_text = re.sub(r'<[^>]+>', ' ', html_text)
    # Remove extra whitespaces
    clean_text = ' '.join(clean_text.split())
    return clean_text

def extract_key_elements(text: str) -> List[str]:
    """
    Extract key elements from the text that should be represented in the image.
    """
    # Split into sentences
    sentences = text.split('.')
    # Take first two sentences as they usually contain the main message
    key_sentences = sentences[:2]
    return [s.strip() for s in key_sentences if s.strip()]

def process_user_post(user_post: str) -> str:
    """
    Process the user's LinkedIn post and generate an appropriate image prompt.
    """
    # Clean HTML content
    clean_text = clean_html_content(user_post)
    
    # Extract key elements
    key_elements = extract_key_elements(clean_text)
    
    # Analyze content
    content_analysis = analyze_post_content(clean_text)
    
    # Generate visual prompt
    return generate_visual_prompt(content_analysis, key_elements)

def resize_image_to_1080(image_bytes):
    image = Image.open(io.BytesIO(image_bytes))
    image = image.resize((1080, 1080), Image.LANCZOS)
    output = io.BytesIO()
    image.save(output, format='JPEG', quality=95)
    return output.getvalue()

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle validation errors and return proper error messages."""
    error_details = []
    for error in exc.errors():
        if error["type"] == "value_error":
            error_details.append(error["msg"])
        elif error["type"] == "missing":
            error_details.append(f"Missing required field: {error['loc'][-1]}")
        elif error["type"] == "string_too_short":
            error_details.append(f"Field '{error['loc'][-1]}' is too short")
        elif error["type"] == "string_too_long":
            error_details.append(f"Field '{error['loc'][-1]}' is too long")
        else:
            error_details.append(f"Validation error in field '{error['loc'][-1]}': {error['msg']}")
    
    error_message = "; ".join(error_details) if error_details else "Invalid request data"
    
    return JSONResponse(
        status_code=400,
        content={
            "success": False,
            "message": f"Bad Request: {error_message}",
            "error_type": "validation_error",
            "details": error_details
        }
    )

@app.get("/")
async def root():
    """Root endpoint providing API information and available endpoints."""
    return {
        "message": "LinkedIn Image & Infographic Generator API",
        "version": "2.1.0",
        "description": "Generate professional images and infographics from LinkedIn content",
        "endpoints": {
            "GET /": "API information (this endpoint)",
            "GET /health": "Health check endpoint",
            "POST /check-compliance": "Check content compliance with LinkedIn policies",
            "POST /generate-image": "Generate professional images using Google Imagen (with compliance check)",
            "POST /generate-infographic": "Generate infographics using OpenAI GPT Image 1.5 (no compliance check)"
        },
        "features": [
            "Content compliance checking (for text-based images)",
            "Context-aware moderation",
            "Professional image generation",
            "Infographic creation with automatic text condensing",
            "Automatic dimension optimization",
            "Hashtag removal for infographics"
        ],
        "compliance_notes": {
            "generate-image": "Compliance check enabled - blocks policy violations",
            "generate-infographic": "Compliance check disabled - visual content with OpenAI safety filters"
        },
        "services": {
            "imagen": "Available" if os.getenv("GOOGLE_CLOUD_PROJECT") else "Not configured",
            "openai_infographics": "Available" if openai_client else "Not configured"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint for deployment monitoring"""
    return {
        "status": "healthy",
        "service": "linkedin-image-infographic-generator",
        "version": "2.0.0",
        "services": {
            "imagen": "operational" if os.getenv("GOOGLE_CLOUD_PROJECT") else "unavailable",
            "openai": "operational" if openai_client else "unavailable",
            "compliance": "operational"
        }
    }

@app.post("/check-compliance")
async def check_compliance(request: PromptRequest):
    """
    Check if the provided content complies with LinkedIn policies.
    This endpoint allows users to verify compliance before generating images.
    """
    try:
        # Use raw content for compliance check
        raw_prompt = request.content.strip()

        # Check compliance
        is_compliant, violations = compliance_filter.check_compliance(raw_prompt)
        violation_summary = compliance_filter.get_violation_summary(violations)
        
        compliance_response = ComplianceResponse(
            is_compliant=is_compliant,
            violations=violations,
            violation_summary=violation_summary
        )
        
        return compliance_response
        
    except Exception as e:
        logger.error(f"Error checking compliance: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error checking compliance: {str(e)}")

@app.post("/generate-image")
async def create_image(request: PromptRequest):
    """
    Generate a professional image for LinkedIn posts using Google's Imagen.
    Includes compliance checking before generation.
    """
    try:
        # Use raw content as the prompt
        raw_prompt = request.content.strip()
        logger.info(f"Using raw prompt: {raw_prompt}")

        # Check compliance before generating image
        is_compliant, violations = compliance_filter.check_compliance(raw_prompt)
        violation_summary = compliance_filter.get_violation_summary(violations)

        if not is_compliant:
            logger.warning(f"Compliance violation detected: {violation_summary}")

            # Return compliance violation response
            compliance_response = ComplianceResponse(
                is_compliant=False,
                violations=violations,
                violation_summary=violation_summary
            )

            # Return 409 Forbidden with detailed compliance info
            return JSONResponse(
                status_code=409,
                content={
                    "success": False,
                    "message": "Image generation blocked due to LinkedIn policy violations. Please review and modify your content.",
                    "error_code": "COMPLIANCE_VIOLATION"
                }
            )

        # Generate the image if compliant
        try:
            image_bytes = generate_image(raw_prompt)
        except RuntimeError as e:
            if "Either image_bytes or gcs_uri must be provided" in str(e):
                raise HTTPException(
                    status_code=503,
                    detail="Image generation temporarily unavailable. Please try again."
                )
            raise HTTPException(status_code=500, detail=str(e))

        # Resize the image to 1080
        resized_image = resize_image_to_1080(image_bytes)

        return StreamingResponse(
            io.BytesIO(resized_image),
            media_type="image/jpeg",
            headers={"Content-Disposition": "attachment; filename=generated_image.jpg"}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating image: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating image: {str(e)}")


def generate_infographic_image(prompt: str, size: str) -> str:
    """
    Generate an infographic image using OpenAI's GPT Image 1.5 model.

    Args:
        prompt: The detailed prompt for infographic generation
        size: Image size (e.g., "1024x1024", "1536x1024", "1024x1536")

    Returns:
        Path to the generated image file, or None if generation fails
    """
    if openai_client is None:
        logger.error("OpenAI client is not initialized.")
        return None

    logger.info(f"Generating infographic image with GPT Image 1.5, size {size}...")
    try:
        response = openai_client.images.generate(
            model="gpt-image-1.5",
            prompt=prompt,
            size=size,
            quality="medium",
            n=1
        )
        logger.info("Received response from GPT Image 1.5.")

        # Get the first image from response
        first_image = response.data[0]
        image_bytes = None

        # Check if response has base64 data or URL
        if hasattr(first_image, 'b64_json') and first_image.b64_json:
            # Base64 format
            logger.info("Image received in base64 format")
            import base64
            image_bytes = base64.b64decode(first_image.b64_json)
        elif hasattr(first_image, 'url') and first_image.url:
            # URL format
            image_url = first_image.url
            logger.info(f"Image URL received: {image_url[:50]}...")

            # Download the image
            import requests
            image_response = requests.get(image_url)
            image_bytes = image_response.content
        else:
            logger.error("Unknown response format from OpenAI API")
            logger.error(f"Available attributes: {dir(first_image)}")
            return None

        if not image_bytes:
            logger.error("Failed to get image bytes from response")
            return None

        # Save to a temporary file
        temp_file = tempfile.NamedTemporaryFile(suffix=".png", delete=False)
        image_path = temp_file.name
        temp_file.close()

        # Write the image bytes
        with open(image_path, 'wb') as f:
            f.write(image_bytes)

        logger.info(f"Infographic image saved successfully to: {image_path} ({len(image_bytes)} bytes)")
        return image_path

    except Exception as e:
        logger.error(f"Error generating infographic with GPT Image 1.5: {e}", exc_info=True)
        return None


@app.post("/generate-infographic")
async def generate_infographic_endpoint(request: PromptRequest):
    """
    Generate a professional infographic from LinkedIn post content using OpenAI's GPT Image 1.5.

    This endpoint:
    1. Preprocesses content (removes hashtags, condenses long text)
    2. Analyzes the content to determine optimal image dimensions
    3. Generates a detailed infographic prompt
    4. Creates the infographic using GPT Image 1.5
    5. Returns the generated image file

    Note: Compliance checking is disabled for infographics as they are visual content
    and may contain legitimate business terms that trigger false positives.

    Parameters:
    - content: The LinkedIn post content to convert into an infographic

    Returns:
    - PNG image file of the generated infographic
    """
    raw_content = request.content.strip()

    if not raw_content:
        logger.warning("Received empty content for infographic generation.")
        raise HTTPException(status_code=400, detail="Input text cannot be empty.")

    # Check if OpenAI client is available
    if openai_client is None:
        logger.error("OpenAI client not initialized.")
        raise HTTPException(
            status_code=503,
            detail="Infographic generation service unavailable. OpenAI API key not configured."
        )

    # Compliance check disabled for infographics to avoid false positives
    # with legitimate business terms (e.g., "fraud detection", "automated systems")
    logger.info("Skipping compliance check for infographic generation (visual content)")

    # Preprocess content: remove hashtags and condense if too long
    logger.info(f"Preprocessing content (original length: {len(raw_content)} chars)...")
    processed_content = preprocess_content(raw_content)
    logger.info(f"Content preprocessed (new length: {len(processed_content)} chars)")
    logger.info(f"Processed content preview: {processed_content[:200]}...")

    # Analyze content to select the best image dimension
    def analyze_content_complexity(text):
        """
        Analyze content complexity to determine optimal image dimensions.
        Returns a tuple of (points_count, content_length, word_count, has_structure).
        """
        lines = [l.strip() for l in text.split('\n') if l.strip()]

        # Count different types of structured content
        bullet_points = [l for l in lines if l.startswith(('- ', '* ', '+ ', '•'))]
        numbered_points = [l for l in lines if len(l) >= 2 and l[:2].replace('.', '').replace(')', '').isdigit()]

        # Get the maximum count of structured points
        points_count = max(len(bullet_points), len(numbered_points), len(lines))

        # Calculate content metrics
        content_length = len(text)
        word_count = len(text.split())

        # Check if content has clear structure (bullets or numbers)
        has_structure = len(bullet_points) > 0 or len(numbered_points) > 0

        return points_count, content_length, word_count, has_structure

    # Analyze the processed content
    points, content_length, word_count, has_structure = analyze_content_complexity(processed_content)

    logger.info(f"Content analysis: {points} points, {content_length} chars, {word_count} words, structured: {has_structure}")

    # Smart dimension selection based on content complexity
    # Cost optimization: 1024x1024 ($0.034) < 1024x1536/1536x1024 ($0.05)
    # Uses smaller dimensions when appropriate to save costs

    # Very short and simple content - use square (cheapest: $0.034)
    if points <= 2 and content_length <= 150 and word_count <= 25:
        size = "1024x1024"
        aspect_ratio = "1:1"
        reason = "very short content (≤2 points, ≤150 chars, ≤25 words) - using square format (cost-effective)"

    # Long content or many points - use landscape ($0.05)
    elif points >= 4 or content_length > 250 or word_count > 40:
        size = "1536x1024"
        aspect_ratio = "16:9"
        reason = f"content-heavy ({points} points, {content_length} chars, {word_count} words) - using landscape"

    # Medium content - use portrait ($0.05)
    else:
        size = "1024x1536"
        aspect_ratio = "9:16"
        reason = f"medium content ({points} points, {content_length} chars, {word_count} words) - using portrait"

    logger.info(f"Selected size: {size}, aspect ratio: {aspect_ratio}")
    logger.info(f"Selection reason: {reason}")

    # Generate the infographic prompt with preprocessed content
    prompt = get_infographic_prompt(processed_content, style="professional", aspect_ratio=aspect_ratio, size=size)
    logger.info(f"Generated infographic prompt (length: {len(prompt)} chars)")

    # Generate the infographic image
    image_path = generate_infographic_image(prompt, size)

    if not image_path:
        logger.error("Infographic image generation failed.")
        raise HTTPException(
            status_code=500,
            detail="Failed to generate infographic image. Please try again."
        )

    # Return the image file using FileResponse
    file_name = f"linkedin_infographic_{uuid.uuid4().hex[:8]}.png"
    logger.info(f"Returning generated infographic: {file_name}")

    return FileResponse(
        path=image_path,
        media_type="image/png",
        filename=file_name,
        headers={
            "Content-Disposition": f"attachment; filename={file_name}"
        }
    )

