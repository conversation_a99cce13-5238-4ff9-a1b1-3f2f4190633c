"""
Prompt generation utilities for infographic creation.
"""
import re
from typing import List


def remove_hashtags(text: str) -> str:
    """
    Remove all hashtags from the text.

    Args:
        text: Input text that may contain hashtags

    Returns:
        Text with all hashtags removed
    """
    # Remove hashtags (words starting with #)
    # This pattern matches # followed by word characters, including underscores
    cleaned_text = re.sub(r'#\w+', '', text)

    # Clean up extra whitespace that may result from hashtag removal
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
    cleaned_text = cleaned_text.strip()

    return cleaned_text


def extract_key_points(text: str, max_points: int = 5) -> List[str]:
    """
    Extract key points from text, limiting to a maximum number.

    Args:
        text: Input text to extract key points from
        max_points: Maximum number of key points to extract (default: 5)

    Returns:
        List of key points
    """
    # Split by newlines and filter empty lines
    lines = [line.strip() for line in text.split('\n') if line.strip()]

    # Identify bullet points or numbered lists
    key_points = []
    for line in lines:
        # Check if line starts with bullet point markers or numbers
        if re.match(r'^[\-\*\+•]\s+', line) or re.match(r'^\d+[\.\)]\s+', line):
            # Remove the marker and add to key points
            cleaned_line = re.sub(r'^[\-\*\+•\d\.\)]+\s+', '', line)
            key_points.append(cleaned_line)
        elif len(line) > 20 and len(key_points) < max_points:
            # If it's a substantial line and we haven't reached max points
            key_points.append(line)

    # If no structured points found, split into sentences and take first few
    if not key_points:
        sentences = re.split(r'[.!?]+', text)
        key_points = [s.strip() for s in sentences if len(s.strip()) > 20][:max_points]

    # Limit to max_points
    return key_points[:max_points]


def condense_content(text: str, max_length: int = 500) -> str:
    """
    Condense long content to essential information only.

    Args:
        text: Input text to condense
        max_length: Maximum character length for condensed content

    Returns:
        Condensed text
    """
    # If text is already short enough, return as is
    if len(text) <= max_length:
        return text

    # Extract key points (limit to 5 for readability)
    key_points = extract_key_points(text, max_points=5)

    # If we have key points, format them nicely
    if key_points:
        # Truncate each key point if it's too long
        max_point_length = 80  # Maximum characters per point
        truncated_points = []
        for point in key_points:
            if len(point) > max_point_length:
                # Truncate at word boundary
                truncated = point[:max_point_length].rsplit(' ', 1)[0] + '...'
                truncated_points.append(truncated)
            else:
                truncated_points.append(point)

        condensed = '\n'.join([f"• {point}" for point in truncated_points])

        # If still too long, reduce number of points
        if len(condensed) > max_length:
            # Try with fewer points
            for num_points in range(4, 0, -1):
                condensed = '\n'.join([f"• {point}" for point in truncated_points[:num_points]])
                if len(condensed) <= max_length:
                    break
    else:
        # Otherwise, take first max_length characters and add ellipsis
        condensed = text[:max_length].rsplit(' ', 1)[0] + '...'

    return condensed


def preprocess_content(content: str) -> str:
    """
    Preprocess content for infographic generation by removing hashtags and condensing text.

    Args:
        content: Raw content from user

    Returns:
        Cleaned and condensed content ready for infographic generation
    """
    # Step 1: Remove hashtags
    cleaned = remove_hashtags(content)

    # Step 2: Condense if too long
    condensed = condense_content(cleaned, max_length=500)

    return condensed


def get_all_layout_options() -> str:
    """
    Returns comprehensive information about all available layout types.
    The AI will intelligently choose the most appropriate layout based on content.
    
    Returns:
        Detailed description of all layout options
    """
    return """
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📐 INTELLIGENT LAYOUT SELECTION (AI-DRIVEN)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**CRITICAL INSTRUCTION**: Analyze the content deeply and select THE MOST APPROPRIATE layout from the options below.
Your choice should feel natural and purpose-built for this specific content. Consider:
- What is the main message or goal?
- What type of information is being presented?
- How should the viewer's eye flow through the content?
- What structure will make the information easiest to understand?

Choose ONE layout type and implement it consistently throughout the entire infographic.

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 LAYOUT OPTION 1: SPLIT-SCREEN COMPARISON
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
**BEST FOR**: Comparisons, before/after, pros/cons, A vs B, contrasting concepts, differences/similarities

**STRUCTURE**:
- Divide canvas into TWO distinct vertical sections (50/50 split or 60/40 if one side needs emphasis)
- **Visual Separator**: Bold vertical line, gradient, or contrasting background colors
- **Symmetry**: Mirror design elements on both sides for visual balance
- **Headers**: Clear labels at top of each section (e.g., "Before vs After", "Option A vs Option B")
- **Icons**: Use contrasting icons/colors for each side to reinforce differences
- **Flow**: Top-to-bottom comparison with aligned elements for easy scanning
- **Color Coding**: Use color psychology (e.g., green for pros/positives, red for cons/negatives)

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 LAYOUT OPTION 2: TIMELINE/CHRONOLOGICAL
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
**BEST FOR**: Historical events, project milestones, evolution over time, roadmaps, sequential dates, progress

**STRUCTURE**:
- **Timeline Line**: Bold, continuous line (horizontal or vertical) connecting all events
- **Event Markers**: Circular nodes or milestone markers at each point along timeline
- **Date/Time Labels**: Prominently display dates/years/stages above or beside each marker
- **Content Cards**: Alternate sides of timeline (zigzag pattern) or keep all on one side
- **Directional Flow**: Arrows showing progression (left-to-right or top-to-bottom)
- **Visual Hierarchy**: Emphasize key milestones with larger markers or different colors
- **Spacing**: Equal spacing between timeline points for visual rhythm
- **Start/End**: Clear beginning and end points

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 LAYOUT OPTION 3: CIRCULAR/RADIAL/CYCLE
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
**BEST FOR**: Continuous processes, recurring cycles, interconnected concepts, hub-and-spoke relationships, loops

**STRUCTURE**:
- **Center Point**: Main concept/title in prominent circle at center
- **Radial Elements**: 4-8 key points arranged in circle around center (like a wheel)
- **Connection**: Curved arrows or lines connecting elements to show flow/relationships
- **Visual Balance**: Distribute elements evenly around circle (equal angles)
- **Numbering**: If showing process, number segments clockwise from top
- **Color Progression**: Gradient or color shift showing movement through cycle
- **Icons**: Place icons inside circular containers at each point
- **Flow Arrows**: Show direction if it's a continuous cycle

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 LAYOUT OPTION 4: PROCESS FLOWCHART
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
**BEST FOR**: Workflows, processes, how-to guides, decision trees, step-by-step procedures, systems

**STRUCTURE**:
- **Flow Direction**: Top-to-bottom or left-to-right with connected boxes
- **Start/End Markers**: Clear visual indicators (rounded for start, bold/colored for end)
- **Process Boxes**: Rectangular containers for each step with consistent sizing
- **Arrows**: Bold directional arrows showing flow between steps
- **Decision Points**: Diamond shapes for Yes/No decisions or branching paths
- **Numbering**: Clear step numbers (1, 2, 3...) inside or beside boxes
- **Color Coding**: Different background colors for different phases
- **Alignment**: Strict grid alignment for professional appearance

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 LAYOUT OPTION 5: HIERARCHICAL/PYRAMID
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
**BEST FOR**: Rankings, top lists, priorities, importance levels, organizational structure, tiered information

**STRUCTURE**:
- **Pyramid Shape**: Most important at top (largest) descending to less important at bottom
- **Visual Sizing**: Higher-priority items are physically larger with bigger fonts/icons
- **Levels**: 3-5 distinct tiers or levels maximum
- **Ranking Numbers**: Clear numbers (#1, #2, #3...) or TOP 5, TOP 10 format
- **Color Intensity**: Use color gradient to show hierarchy (darker/brighter = more important)
- **Spacing**: More space around top items, condensed as you go down
- **Shape Variations**: Pyramid, triangle, stepped blocks, or descending containers

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 LAYOUT OPTION 6: STATISTICAL/DATA-DRIVEN
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
**BEST FOR**: Statistics, survey results, data insights, percentages, metrics, numerical information

**STRUCTURE**:
- **Hero Number**: Feature most important statistic LARGE at top (e.g., "85%" in huge font)
- **Chart Types**: Bar charts (comparisons), pie charts (percentages), line graphs (trends)
- **Data Labels**: ALL data points must have clear, visible labels with values
- **Grid Background**: Light grid lines for charts to aid reading
- **Color Coding**: Consistent colors for data categories throughout
- **Context Text**: Brief explanatory text under each chart/statistic
- **Flow Pattern**: Z-pattern or F-pattern for eye movement
- **Visual Mix**: Combine charts with iconography and numbers for visual interest

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 LAYOUT OPTION 7: VERTICAL LIST/SEQUENTIAL
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
**BEST FOR**: Tips, bullet points, multiple separate ideas, features, benefits, key takeaways

**STRUCTURE**:
- **Vertical Stack**: Items arranged top-to-bottom, each in own container/card
- **Item Cards**: Distinct rectangular or rounded containers for each point
- **Numbering/Bullets**: Clear numbers (1, 2, 3...) or bullet points for each item
- **Icon Placement**: Icon on left or top of each card for visual interest
- **Text Hierarchy**: Bold header for each point + brief description
- **Spacing**: Consistent vertical spacing between all items (breathing room)
- **Visual Flow**: Top-to-bottom progression with visual continuity
- **Alternating Pattern**: Consider alternating background colors or icon positions for rhythm
- **Optimal Count**: 5-7 items for best readability

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 LAYOUT OPTION 8: STEP-BY-STEP SEQUENTIAL
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
**BEST FOR**: Instructions, tutorials, ordered processes, sequential actions, guided procedures

**STRUCTURE**:
- **Step Numbers**: LARGE, bold numbers (1, 2, 3...) prominently displayed
- **Step Containers**: Each step in distinct visual box/card
- **Connecting Arrows**: Arrows between steps showing clear progression
- **Flow Direction**: Choose left-to-right OR top-to-bottom (stay consistent)
- **Icons Per Step**: Unique icon representing each step's action
- **Action Verbs**: Start each step with clear action verb (Create, Design, Build, Launch)
- **Color Progression**: Gradient showing progress (light to dark or cool to warm)
- **Final Step Emphasis**: Make final step visually distinct to show completion

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 LAYOUT OPTION 9: ADAPTIVE GRID (FLEXIBLE)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
**BEST FOR**: Multiple equal-weight items, features grid, concept showcase, balanced information

**STRUCTURE**:
- **Grid Logic**: Adapt to content count
  * 2-3 items: Large cards in 1-2 columns (generous space per item)
  * 4-6 items: 2x2 or 2x3 grid layout (balanced distribution)
  * 7-9 items: 3x3 grid layout (compact but readable)
  * 10+ items: Consider condensing content or using 3x4/4x4 grid
- **Card Consistency**: All cards/containers equal size within grid
- **Gutters**: Equal spacing between all grid items (30-40px)
- **Alignment**: Perfect alignment on both horizontal and vertical axes
- **Visual Rhythm**: Consistent icon placement and text hierarchy throughout
- **Flexibility**: Allow cards to span multiple columns if needed for emphasis
- **Balance**: Evenly distribute visual weight across canvas

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🤖 YOUR TASK: INTELLIGENT LAYOUT SELECTION
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

1. **ANALYZE** the content carefully - understand its nature, structure, and message
2. **CHOOSE** the MOST APPROPRIATE layout from the 9 options above
3. **IMPLEMENT** that layout consistently and professionally throughout the entire infographic
4. **ADAPT** the layout details to perfectly fit the specific content (be flexible within the structure)

**IMPORTANT**: Your layout choice should feel intentional and natural, not forced.
If the content could work with multiple layouts, choose the one that will be most visually engaging and easy to understand.
Make it feel CUSTOM-DESIGNED for this specific content, not generic.
"""


def get_infographic_prompt(content: str, style: str = "professional", aspect_ratio: str = "1:1", size: str = "1536x1024") -> str:
    """
    Returns an enhanced prompt for GPT Image 1.5 to generate premium, visually rich, LinkedIn-ready infographics.
    Incorporates 2024/2025 best practices: single clear focus, visual consistency, narrative flow, optimal white space,
    limited fonts (2-3 max), data visualization, proper alignment, and professional color schemes.
    Now with AI-DRIVEN DYNAMIC LAYOUT SELECTION - the AI intelligently chooses the best layout for the content.
    """
    # Get all layout options for AI to choose from
    layout_options = get_all_layout_options()
    
    prompt = f'''
You are an elite infographic designer with expertise in visual storytelling, data visualization, and modern design principles. Create a premium LinkedIn-ready infographic (size: {size}, aspect ratio: {aspect_ratio}) based on this content:

"""{content}"""

{layout_options}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 CORE DESIGN PRINCIPLES (CRITICAL)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

## 1. SINGLE CLEAR FOCUS
- Communicate ONE compelling idea or message clearly
- Every element should support this central theme
- Avoid mixing multiple unrelated topics
- The viewer should understand the main point in 3 seconds

## 2. VISUAL STORYTELLING & NARRATIVE FLOW
- Structure: Engaging Title → Introduction → Main Points → Conclusion/CTA
- Create a logical progression that guides the eye naturally (top-to-bottom or left-to-right)
- Use visual hierarchy to show importance (size, color, position)
- Each section should flow smoothly to the next with clear connections
- Consider using numbered sequences (1, 2, 3) or directional arrows for flow

## 3. BALANCE: TEXT VS. VISUALS (70% VISUAL, 30% TEXT)
- SHOW, DON'T TELL: Prioritize icons, illustrations, charts over lengthy text
- Maximum 3-5 key points, each 5-12 words
- Use visual metaphors and symbols to communicate ideas
- Replace text with relevant icons wherever possible
- For longer content: summarize only the MOST important points

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎨 COLOR & VISUAL CONSISTENCY
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

## COLOR PALETTE (2-3 PRIMARY COLORS MAX)
Analyze content type and select ONE cohesive palette:

**Professional/Corporate**: Navy Blue (#0A2463) + Bright Blue (#3E92CC) + Light Gray (#F5F5F5)
**Tech/Innovation**: Electric Blue (#1E88E5) + Cyan (#00BCD4) + White (#FFFFFF)
**Creative/Inspiring**: Purple (#6A4C93) + Coral (#FF6B6B) + Cream (#FFF8E7)
**Growth/Success**: Forest Green (#2D6A4F) + Gold (#FFB703) + Off-White (#F8F9FA)
**Marketing/Energy**: Orange (#FF6B35) + Deep Purple (#4A0E4E) + Light Background (#FFF4E6)
**Minimal/Modern**: Black (#1A1A1A) + Accent Color (choose 1: red/blue/green) + White (#FFFFFF)

- Apply colors consistently throughout (backgrounds, icons, text highlights, containers)
- Ensure high contrast: dark text on light backgrounds or vice versa (WCAG AA minimum)
- Use one color as primary (60%), another as secondary (30%), third for accents (10%)

## VISUAL CONSISTENCY
- Use the SAME icon style throughout (flat, line, 3D - pick ONE)
- Maintain consistent shapes (all rounded corners OR all sharp - not mixed)
- Keep visual weight balanced across sections
- Repeat design elements to create rhythm and unity

## PROFESSIONAL AESTHETIC (NO CARTOON STYLE)
- ❌ AVOID: Cartoonish illustrations, comic-style graphics, playful characters, clip art
- ❌ AVOID: Overly rounded "bubbly" designs, childish elements, hand-drawn sketches
- ✅ USE: Modern minimalist icons (line icons or solid flat icons)
- ✅ USE: Professional geometric shapes, clean abstract visuals
- ✅ USE: Sleek, corporate, sophisticated design language
- ✅ USE: Realistic photography (if images used), professional illustrations
- Style: Think "Fortune 500 presentation" not "children's book"

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📐 SPACING, WHITE SPACE & ALIGNMENT (CRITICAL)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

## WHITE SPACE MASTERY
- Use AMPLE white space (negative space) to prevent visual clutter
- White space is NOT wasted space - it improves readability by 20%
- Leave generous margins: minimum 40-60px from all edges
- Create breathing room between sections (30-50px spacing)
- Don't fill every pixel - embrace emptiness for elegance
- **ADAPT spacing to the selected layout type** - different layouts need different breathing room

## ALIGNMENT & STRUCTURE
- Follow the SELECTED LAYOUT structure precisely (defined above)
- Use invisible grid system for perfect alignment within your chosen layout
- Align elements to consistent baselines appropriate for the layout type
- Group related items together with proximity
- Create clear visual sections with spacing/dividers that complement the layout
- Maintain equal padding within containers
- **Layout consistency**: Stay true to the selected layout pattern throughout

## ELEMENT PLACEMENT
- Never cut off or truncate text - if it doesn't fit, reduce content
- Keep all text and icons at least 50px from edges
- Ensure no overlapping elements unless intentionally layered
- All labels, numbers, and data points must be fully visible
- Position elements according to the layout flow (vertical, horizontal, circular, etc.)

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✍️ TYPOGRAPHY (2-3 FONTS MAXIMUM)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

## FONT PAIRING (Choose ONE pair):
- **Modern Professional**: Poppins (Bold) + Inter (Regular)
- **Corporate**: Montserrat (Bold) + Open Sans (Regular)
- **Creative**: Playfair Display (Bold) + Lato (Regular)
- **Tech**: Raleway (Bold) + Roboto (Regular)
- **Bold Statement**: Oswald (Bold) + Source Sans Pro (Regular)
- **Editorial**: Abril Fatface (Bold) + Inter (Regular)

## HIERARCHY (Size & Weight):
- **Main Title**: 36-48pt, Bold, Dark color - captures the BIG idea
- **Section Headers**: 24-30pt, Semi-Bold
- **Body Text/Key Points**: 16-20pt, Regular weight
- **Supporting Text**: 12-14pt, Light weight
- **Captions/Labels**: 10-12pt

## READABILITY RULES:
- Never use ALL CAPS for body text (only for short labels/titles)
- Maintain proper line spacing (1.4-1.6 line height)
- Left-align text for readability (center-align only titles)
- Ensure contrast ratio of at least 4.5:1 for text

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 DATA VISUALIZATION & ICONS
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

## ICONS & ILLUSTRATIONS
- Use a unique, high-quality icon for EVERY key point (consistent style)
- Icon size: 48-64px, with consistent visual weight
- Place icons in colored circular or square containers for emphasis
- Choose meaningful, intuitive icons that enhance understanding
- Avoid generic or overused stock icons
- **ICON STYLE**: Modern, minimalist, professional (line icons or flat solid icons)
- **NO cartoon style**: Use clean, simple, business-appropriate icons only
- Examples of good styles: Material Design icons, Feather icons, corporate icon sets
- Think: LinkedIn, Microsoft, Apple aesthetics - NOT playful or childish

## DATA VISUALIZATION (when applicable):
- Bar charts: for comparisons, use consistent bar width
- Pie charts: for percentages, limit to 5 segments max
- Line graphs: for trends over time, clear axis labels
- Simple illustrations: to represent concepts visually
- All charts must have: clear labels, legend, and data values visible
- Use the same color palette as the overall design

## VISUAL CONTAINERS
- Each key point gets its own distinct visual container/card
- Use rounded rectangles, circles, or geometric shapes
- Add subtle shadows or borders for depth (optional)
- Color-code containers by theme or importance

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎭 TITLE & CONTENT CREATION
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

## TITLE GENERATION (Examples):
- "Tips for portfolio..." → **"5 Portfolio Design Strategies That Get You Hired"**
- "AI tools discussion..." → **"Essential AI Tools Every Professional Should Use"**
- "Business growth..." → **"How We Scaled to 10K Users in 90 Days"**

Make it: Specific, Benefit-Driven, Action-Oriented, and Compelling

## LANGUAGE QUALITY
- ✓ Fluent, natural, error-free English
- ✓ Simple everyday language (8th-grade reading level)
- ✓ Active voice over passive voice
- ✓ No hashtags anywhere in the infographic
- ✓ Proofread every word for spelling and grammar

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ FINAL QUALITY CHECKLIST
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

Before finalizing, verify:
☐ Single clear message communicated effectively
☐ Logical flow from start to finish
☐ 2-3 colors used consistently throughout
☐ 2-3 fonts maximum (proper hierarchy)
☐ Ample white space, no clutter
☐ All elements perfectly aligned on grid
☐ Every key point has a relevant icon
☐ High contrast for readability
☐ No text cut off or truncated
☐ Professional, polished, LinkedIn-appropriate
☐ Could be understood in 5 seconds or less

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎨 OUTPUT REQUIREMENTS
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

Create a complete, finished infographic image (NOT HTML/CSS/code):
- High resolution, print quality
- Optimized for LinkedIn sharing and engagement
- Aspect ratio: {aspect_ratio}
- Image dimensions: {size}
- Format: Clean, modern, professional
- **AESTHETIC**: Sleek, corporate, sophisticated - NO cartoon or playful styles
- **DESIGN REFERENCE**: Think Fortune 500, consulting firms, tech giants (Apple, Microsoft, LinkedIn)
- This is a true INFOGRAPHIC: 70% visuals, 30% text maximum
- SHOW the story, don't just tell it with words
- **DYNAMIC LAYOUT**: Choose and implement the most appropriate layout from the options above
- **CUSTOM-BUILT FEEL**: Make it feel purpose-designed for THIS specific content, not a generic template

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🚀 FINAL INSTRUCTION
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

Now create this stunning, professional, business-appropriate infographic!

**YOUR MISSION**:
1. Analyze the content and understand its core message
2. Select the BEST layout type from the 9 options provided
3. Implement that layout with excellence and consistency
4. Make every design decision enhance understanding and engagement

The result should feel like it was CUSTOM-DESIGNED by a professional designer specifically for this content.
Make it command attention, credibility, and shareability on LinkedIn! 💼
'''
    return prompt.strip()

